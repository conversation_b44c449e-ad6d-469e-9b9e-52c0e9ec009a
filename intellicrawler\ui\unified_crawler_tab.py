#!/usr/bin/env python3
"""
Unified Crawler-Analysis Tab for IntelliCrawler
Combines web crawling and AI analysis functionality in a single interface.
"""

import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (
    QWidget, QVBoxLayout, QHBoxLayout, QGridLayout, QSplitter, QFormLayout,
    QGroupBox, QLabel, QLineEdit, QPushButton, QTextEdit, QProgressBar,
    QTabWidget, QTableWidget, QTableWidgetItem, QComboBox, QSpinBox,
    QCheckBox, QSlider, QFrame, QScrollArea, QTreeWidget, QTreeWidgetItem,
    QHeaderView, QMessageBox, QFileDialog, QApplication, QSizePolicy
)
from PyQt5.QtCore import Qt, QTimer, pyqtSignal, QThread, pyqtSlot
from PyQt5.QtGui import QFont, QPixmap, QIcon

from intellicrawler.utils.logger import get_logger
from intellicrawler.utils.error_handler import try_except_with_dialog
from intellicrawler.utils.data_persistence import DataPersistenceManager
from intellicrawler.ai_integration import DeepSeekAI
from intellicrawler.ui.analysis_tab import AnalysisWorker

class UnifiedCrawlerAnalysisTab(QWidget):
    """Unified tab combining web crawling and AI analysis functionality"""
    
    # Signals
    browser_activity = pyqtSignal(str, str)  # action, url
    intelligent_decision = pyqtSignal(str)  # AI decision message
    analysis_completed = pyqtSignal(dict)  # analysis results
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = get_logger(__name__)
        
        # Data storage
        self.scraped_data = []
        self.analysis_results = {}
        self.is_running = False
        self.using_ai_mode = False
        self.current_crawling_strategy = None
        self.browser_activity_log = []
        
        # Initialize components
        self.persistence_manager = DataPersistenceManager()
        self.ai_agent = None
        self.analysis_worker = None
        
        # Setup UI
        self.setup_ui()
        
        # Timer for real-time updates
        self.update_timer = QTimer()
        self.update_timer.timeout.connect(self.update_real_time_display)
        
        # Session tracking
        self.session_start_time = None
        self.session_timer = QTimer()
        self.session_timer.timeout.connect(self.update_session_timer)
        
    def setup_ui(self):
        """Setup the unified interface"""
        layout = QVBoxLayout(self)
        layout.setContentsMargins(10, 10, 10, 10)
        layout.setSpacing(10)
        
        # Top control panel
        self.setup_control_panel(layout)
        
        # Main content area with splitter
        self.setup_main_content_area(layout)
        
        # Bottom status panel
        self.setup_status_panel(layout)
        
    def setup_control_panel(self, parent_layout):
        """Setup the top control panel"""
        control_frame = QFrame()
        control_frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(control_frame)
        
        # URL Input  Settings Group
        url_group = QGroupBox("🌐 Target  Settings")
        url_form_layout = QFormLayout(url_group)
        
        # URL input
        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("Enter website URL to crawl...")
        url_form_layout.addRow("URL:", self.url_input)
        
        # Settings row
        settings_layout = QHBoxLayout()
        
        # Crawl settings
        settings_layout.addWidget(QLabel("Max Pages:"))
        self.max_pages_spin = QSpinBox()
        self.max_pages_spin.setRange(1, 1000)
        self.max_pages_spin.setValue(50)
        settings_layout.addWidget(self.max_pages_spin)
        
        # AI settings
        settings_layout.addWidget(QLabel("AI Model:"))
        self.ai_model_combo = QComboBox()
        self.ai_model_combo.addItems(["deepseek-chat", "deepseek-coder"])
        settings_layout.addWidget(self.ai_model_combo)
        
        # Auto-analyze checkbox
        self.auto_analyze_check = QCheckBox("🧠 Auto-Analyze Results")
        self.auto_analyze_check.setChecked(True)
        settings_layout.addWidget(self.auto_analyze_check)
        
        settings_layout.addStretch()
        
        # Add settings row to form layout
        settings_widget = QWidget()
        settings_widget.setLayout(settings_layout)
        url_form_layout.addRow(settings_widget)
        
        # Control Buttons Group
        controls_group = QGroupBox("🚀 Controls")
        controls_layout = QHBoxLayout(controls_group)

        # Ensure controls expand
        controls_group.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Fixed)
        
        # Main action buttons
        self.start_btn = QPushButton("🚀 Start Intelligent Crawl")
        self.start_btn.setStyleSheet("QPushButton { background-color: #4CAF50; color: white; font-weight: bold; padding: 8px; }")
        self.start_btn.clicked.connect(self.start_intelligent_crawl)
        controls_layout.addWidget(self.start_btn)
        
        self.stop_btn = QPushButton("⏹️ Stop")
        self.stop_btn.setEnabled(False)
        self.stop_btn.clicked.connect(self.stop_crawling)
        controls_layout.addWidget(self.stop_btn)
        
        self.reset_btn = QPushButton("🔄 Reset")
        self.reset_btn.clicked.connect(self.reset)
        controls_layout.addWidget(self.reset_btn)
        
        # Analysis buttons
        controls_layout.addWidget(QFrame())  # Separator
        
        self.analyze_btn = QPushButton("🧠 Analyze Now")
        self.analyze_btn.setEnabled(False)
        self.analyze_btn.clicked.connect(self.start_analysis)
        controls_layout.addWidget(self.analyze_btn)
        
        self.summary_btn = QPushButton("📊 Generate Summary")
        self.summary_btn.setEnabled(False)
        self.summary_btn.clicked.connect(self.generate_summary)
        controls_layout.addWidget(self.summary_btn)
        
        # Export buttons
        controls_layout.addWidget(QFrame())  # Separator
        
        self.export_btn = QPushButton("💾 Export Data")
        self.export_btn.setEnabled(False)
        self.export_btn.clicked.connect(self.export_data)
        controls_layout.addWidget(self.export_btn)
        
        # Add groups to layout
        layout.addWidget(url_group)
        layout.addWidget(controls_group)
        
        parent_layout.addWidget(control_frame)
        
    def setup_main_content_area(self, parent_layout):
        """Setup the main content area with splitter"""
        # Create horizontal splitter
        splitter = QSplitter(Qt.Horizontal)
        
        # Left panel - Real-time Activity
        self.setup_activity_panel(splitter)
        
        # Right panel - Results & Analysis
        self.setup_results_panel(splitter)
        
        # Set splitter proportions
        splitter.setSizes([400, 600])
        splitter.setStretchFactor(0, 0)
        splitter.setStretchFactor(1, 1)
        
        parent_layout.addWidget(splitter)
        
    def setup_activity_panel(self, parent_splitter):
        """Setup the left activity panel"""
        activity_frame = QFrame()
        activity_frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(activity_frame)
        
        # Activity tabs
        self.activity_tabs = QTabWidget()
        
        # Crawling Progress Tab
        progress_widget = QWidget()
        progress_layout = QVBoxLayout(progress_widget)
        
        # Progress info
        self.progress_label = QLabel("Ready to start crawling...")
        progress_layout.addWidget(self.progress_label)
        
        self.progress_bar = QProgressBar()
        self.progress_bar.setValue(0)  # Initialize to 0%
        progress_layout.addWidget(self.progress_bar)
        
        # Statistics
        stats_group = QGroupBox("📊 Live Statistics")
        stats_layout = QGridLayout(stats_group)
        
        self.pages_found_label = QLabel("Pages Found: 0")
        self.pages_processed_label = QLabel("Pages Processed: 0")
        self.ai_decisions_label = QLabel("AI Decisions: 0")
        self.data_saved_label = QLabel("Data Saved: 0 KB")
        
        stats_layout.addWidget(self.pages_found_label, 0, 0)
        stats_layout.addWidget(self.pages_processed_label, 0, 1)
        stats_layout.addWidget(self.ai_decisions_label, 1, 0)
        stats_layout.addWidget(self.data_saved_label, 1, 1)
        
        progress_layout.addWidget(stats_group)
        progress_layout.addStretch()
        
        self.activity_tabs.addTab(progress_widget, "🕷️ Progress")
        
        # Browser Activity Log Tab
        self.browser_log = QTextEdit()
        self.browser_log.setReadOnly(True)
        self.activity_tabs.addTab(self.browser_log, "🌐 Browser Log")
        
        # AI Decision Log Tab
        self.ai_log = QTextEdit()
        self.ai_log.setReadOnly(True)
        self.activity_tabs.addTab(self.ai_log, "🤖 AI Decisions")
        
        layout.addWidget(self.activity_tabs)
        parent_splitter.addWidget(activity_frame)
        
    def setup_results_panel(self, parent_splitter):
        """Setup the right results panel"""
        results_frame = QFrame()
        results_frame.setFrameStyle(QFrame.StyledPanel)
        
        layout = QVBoxLayout(results_frame)
        
        # Results tabs
        self.results_tabs = QTabWidget()
        
        # Crawled Data Tab
        self.setup_crawled_data_tab()
        
        # Analysis Results Tab
        self.setup_analysis_results_tab()
        
        # Content Insights Tab
        self.setup_insights_tab()
        
        layout.addWidget(self.results_tabs)
        parent_splitter.addWidget(results_frame)
        
    def setup_crawled_data_tab(self):
        """Setup crawled data display tab"""
        data_widget = QWidget()
        layout = QVBoxLayout(data_widget)
        
        # Search and filter
        search_layout = QHBoxLayout()
        search_layout.addWidget(QLabel("🔍 Search:"))
        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Search crawled content...")
        self.search_input.textChanged.connect(self.filter_results)
        search_layout.addWidget(self.search_input)
        layout.addLayout(search_layout)
        
        # Data table
        self.data_table = QTableWidget()
        self.data_table.setColumnCount(4)
        self.data_table.setHorizontalHeaderLabels(["URL", "Title", "Content Preview", "Status"])
        
        # Configure table
        header = self.data_table.horizontalHeader()
        header.setSectionResizeMode(0, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(1, QHeaderView.ResizeToContents)
        header.setSectionResizeMode(2, QHeaderView.Stretch)
        header.setSectionResizeMode(3, QHeaderView.ResizeToContents)
        
        layout.addWidget(self.data_table)
        
        self.results_tabs.addTab(data_widget, "📄 Crawled Data")
        
    def setup_analysis_results_tab(self):
        """Setup analysis results display tab"""
        analysis_widget = QWidget()
        layout = QVBoxLayout(analysis_widget)
        
        # Analysis controls
        controls_layout = QHBoxLayout()
        
        controls_layout.addWidget(QLabel("Analysis Type:"))
        self.analysis_type_combo = QComboBox()
        self.analysis_type_combo.addItems([
            "comprehensive", "summary", "keywords", "sentiment", "topics"
        ])
        controls_layout.addWidget(self.analysis_type_combo)
        
        controls_layout.addWidget(QLabel("Temperature:"))
        self.temperature_slider = QSlider(Qt.Horizontal)
        self.temperature_slider.setRange(0, 100)
        self.temperature_slider.setValue(70)
        controls_layout.addWidget(self.temperature_slider)
        
        self.temp_label = QLabel("0.7")
        controls_layout.addWidget(self.temp_label)
        self.temperature_slider.valueChanged.connect(
            lambda v: self.temp_label.setText(f"{v/100:.1f}")
        )
        
        controls_layout.addStretch()
        layout.addLayout(controls_layout)
        
        # Analysis results display
        self.analysis_display = QTextEdit()
        self.analysis_display.setReadOnly(True)
        self.analysis_display.setPlaceholderText("Analysis results will appear here after crawling and analysis...")
        layout.addWidget(self.analysis_display)
        
        self.results_tabs.addTab(analysis_widget, "🧠 AI Analysis")
        
    def setup_insights_tab(self):
        """Setup content insights tab"""
        insights_widget = QWidget()
        layout = QVBoxLayout(insights_widget)
        
        # Insights will be populated after analysis
        self.insights_display = QTextEdit()
        self.insights_display.setReadOnly(True)
        self.insights_display.setPlaceholderText("Content insights and statistics will appear here...")
        layout.addWidget(self.insights_display)
        
        self.results_tabs.addTab(insights_widget, "📈 Insights")
        
    def setup_status_panel(self, parent_layout):
        """Setup the bottom status panel"""
        status_frame = QFrame()
        status_frame.setFrameStyle(QFrame.StyledPanel)
        status_frame.setMaximumHeight(60)
        
        layout = QHBoxLayout(status_frame)
        
        # Status message
        self.status_label = QLabel("Ready to start intelligent crawling...")
        layout.addWidget(self.status_label)
        
        layout.addStretch()
        
        # Auto-save status
        self.autosave_label = QLabel("💾 Auto-save: Ready")
        layout.addWidget(self.autosave_label)
        
        # Session timer
        self.session_timer_label = QLabel("⏱️ Session: 00:00:00")
        layout.addWidget(self.session_timer_label)
        
        parent_layout.addWidget(status_frame)
        
    @try_except_with_dialog
    def start_intelligent_crawl(self, checked=False):
        """Start the intelligent AI-powered crawl with integrated analysis"""
        url = self.url_input.text().strip()
        if not url:
            QMessageBox.warning(self, "Input Required", "Please enter a URL to crawl.")
            return
            
        if not url.startswith(('http://', 'https://')):
            url = 'https://' + url
            self.url_input.setText(url)
        
        # Start session tracking
        self.session_start_time = datetime.now()
        self.session_timer.start(1000)  # Update every second
        
        # Update UI state
        self.is_running = True
        self.start_btn.setEnabled(False)
        self.stop_btn.setEnabled(True)
        self.analyze_btn.setEnabled(False)
        self.export_btn.setEnabled(False)
        
        # Clear previous data
        self.scraped_data = []
        self.analysis_results = {}
        self.data_table.setRowCount(0)
        self.analysis_display.clear()
        self.insights_display.clear()
        
        # Update status
        self.status_label.setText(f"🚀 Starting intelligent crawl of {url}...")
        self.progress_bar.setValue(0)
        
        # Start real-time updates
        self.update_timer.start(1000)
        
        # Initialize and start AI agent
        try:
            max_pages = self.max_pages_spin.value()

            # Get or create AI instance
            if hasattr(self.parent(), 'ai_integration'):
                self.ai_agent = self.parent().ai_integration
            else:
                self.ai_agent = DeepSeekAI()

            # Check API key
            if not self.ai_agent.is_api_key_set():
                error_msg = "DeepSeek API key not configured. Please set it in Settings."
                self.add_browser_log(f"❌ {error_msg}")
                raise ValueError(error_msg)

            # Connect signals
            self.ai_agent.agent_progress.connect(self.on_progress_updated)
            self.ai_agent.agent_completed.connect(self.on_crawl_completed)
            self.ai_agent.error_occurred.connect(self.on_error_occurred)
            self.ai_agent.intelligent_decision.connect(self.on_intelligent_decision)

            # Start enhanced AI agent
            success = self.ai_agent.start_enhanced_ai_agent(
                url=url,
                crawl_mode='general',
                intelligence_level=3,
                max_pages=max_pages,
                depth=3,
                model=self.ai_model_combo.currentText()
            )

            if success:
                self.add_browser_log(f"🚀 Started intelligent crawl: {url}")
                self.add_ai_log("🤖 Enhanced AI agent initialized and started")
            else:
                raise Exception("Failed to start AI agent")

        except Exception as e:
            self.logger.error(f"Failed to start crawling: {str(e)}")
            self.status_label.setText(f"❌ Failed to start: {str(e)}")
            self.reset_ui_state()
    
    def stop_crawling(self):
        """Stop the current crawling operation"""
        if self.ai_agent and hasattr(self.ai_agent, 'stop_ai_agent'):
            self.ai_agent.stop_ai_agent()
            self.add_browser_log("⏹️ Crawling stopped by user")

        self.reset_ui_state()
        self.status_label.setText("⏹️ Crawling stopped")
    
    def reset(self):
        """Reset the interface to initial state"""
        self.stop_crawling()
        
        # Clear all data
        self.scraped_data = []
        self.analysis_results = {}
        self.browser_activity_log = []
        
        # Clear UI
        self.data_table.setRowCount(0)
        self.browser_log.clear()
        self.ai_log.clear()
        self.analysis_display.clear()
        self.insights_display.clear()
        
        # Reset progress
        self.progress_bar.setValue(0)
        self.progress_label.setText("Ready to start crawling...")
        self.status_label.setText("Ready to start intelligent crawling...")
        
        # Reset statistics
        self.update_statistics(0, 0, 0, 0)
        
        # Stop timers
        self.update_timer.stop()
        self.session_timer.stop()
        self.session_start_time = None
        self.session_timer_label.setText("⏱️ Session: 00:00:00")
        
        self.add_browser_log("🔄 Interface reset")
    
    def reset_ui_state(self):
        """Reset UI button states"""
        self.is_running = False
        self.start_btn.setEnabled(True)
        self.stop_btn.setEnabled(False)
        self.update_timer.stop()
        
        if self.scraped_data:
            self.analyze_btn.setEnabled(True)
            self.export_btn.setEnabled(True)
    
    @pyqtSlot(str, int)
    def on_progress_updated(self, message, progress):
        """Handle progress updates from AI agent"""
        self.progress_bar.setValue(progress)
        self.progress_label.setText(message)
        self.add_browser_log(f"📊 {message} ({progress}%)")

    @pyqtSlot(dict)
    def on_page_scraped(self, page_data):
        """Handle new page data from AI agent"""
        self.scraped_data.append(page_data)
        self.update_data_table()
        self.update_statistics()

        # Add to browser log
        url = page_data.get('url', 'Unknown')
        title = page_data.get('title', 'Untitled')
        self.add_browser_log(f"📄 Scraped: {title} ({url})")

    @pyqtSlot(list)
    def on_crawl_completed(self, data):
        """Handle crawl completion"""
        self.scraped_data = data
        self.update_data_table()

        # Auto-save data
        try:
            session_id = self.persistence_manager.save_session_data(data)
            self.autosave_label.setText(f"💾 Auto-saved: {session_id}")
            self.add_browser_log(f"💾 Data automatically saved (Session: {session_id})")
        except Exception as e:
            self.autosave_label.setText("💾 Auto-save failed")
            self.add_browser_log(f"⚠️ Auto-save failed: {str(e)}")

        # Update UI
        self.reset_ui_state()
        self.status_label.setText(f"✅ Crawl completed: {len(data)} pages found")
        self.progress_bar.setValue(100)

        # Auto-analyze if enabled
        if self.auto_analyze_check.isChecked() and data:
            self.start_analysis()

        self.add_browser_log(f"✅ Crawl completed! Found {len(data)} relevant pages")

    @pyqtSlot(str)
    def on_error_occurred(self, error_message):
        """Handle errors from AI agent"""
        self.add_browser_log(f"❌ Error: {error_message}")
        self.status_label.setText(f"❌ Error: {error_message}")
        self.reset_ui_state()

    @pyqtSlot(str)
    def on_intelligent_decision(self, decision_message):
        """Handle intelligent decisions from AI agent"""
        self.add_ai_log(f"🧠 {decision_message}")
        self.intelligent_decision.emit(decision_message)

    def start_analysis(self):
        """Start AI analysis of crawled data"""
        if not self.scraped_data:
            QMessageBox.information(self, "No Data", "No crawled data available for analysis.")
            return

        # Get analysis settings
        analysis_type = self.analysis_type_combo.currentText()
        model = self.ai_model_combo.currentText()
        temperature = self.temperature_slider.value() / 100.0

        # Update UI
        self.analyze_btn.setEnabled(False)
        self.analysis_display.setText("🧠 Analyzing content with AI...")
        self.status_label.setText("🧠 Running AI analysis...")

        # Start analysis worker
        try:
            # Use the same AI instance that was used for crawling
            ai_instance = None
            if hasattr(self, 'ai_agent') and self.ai_agent:
                ai_instance = self.ai_agent
                self.add_ai_log("✅ Using existing AI agent for analysis")
            elif hasattr(self.parent(), 'get_ai_instance'):
                ai_instance = self.parent().get_ai_instance()
                self.add_ai_log("✅ Using parent AI instance for analysis")
            else:
                # Create a new AI instance as fallback
                ai_instance = DeepSeekAI()
                self.add_ai_log("✅ Created new AI instance for analysis")

            # Validate AI instance and API key
            if not ai_instance:
                raise Exception("Failed to get AI instance for analysis")

            if not ai_instance.is_api_key_set():
                raise Exception("DeepSeek API key not configured. Please set it in Settings.")

            # Prepare content for analysis
            content = "\n\n".join([
                f"Title: {item.get('title', 'No title')}\nURL: {item.get('url', 'No URL')}\nContent: {item.get('content', 'No content')}"
                for item in self.scraped_data
            ])

            self.analysis_worker = AnalysisWorker(
                ai_instance,
                content,
                analysis_type,
                model,
                temperature
            )

            self.analysis_worker.analysis_completed.connect(self.on_analysis_completed)
            self.analysis_worker.error_occurred.connect(self.on_analysis_error)
            self.analysis_worker.start()

            self.add_ai_log(f"🧠 Started {analysis_type} analysis with {model} (temp: {temperature})")

        except Exception as e:
            self.on_analysis_error(str(e))

    @pyqtSlot(dict)
    def on_analysis_completed(self, results):
        """Handle completed analysis"""
        self.analysis_results = results

        # Display results
        analysis_text = results.get('analysis', 'No analysis results available.')
        self.analysis_display.setText(analysis_text)

        # Update insights
        self.update_insights(results)

        # Update UI
        self.analyze_btn.setEnabled(True)
        self.summary_btn.setEnabled(True)
        self.status_label.setText("✅ AI analysis completed")

        # Switch to analysis tab
        self.results_tabs.setCurrentIndex(1)

        self.add_ai_log("✅ AI analysis completed successfully")

    @pyqtSlot(str)
    def on_analysis_error(self, error_message):
        """Handle analysis errors"""
        self.analysis_display.setText(f"❌ Analysis failed: {error_message}")
        self.analyze_btn.setEnabled(True)
        self.status_label.setText(f"❌ Analysis failed: {error_message}")

        self.add_ai_log(f"❌ Analysis error: {error_message}")

    def generate_summary(self):
        """Generate a quick summary of crawled data"""
        if not self.scraped_data:
            QMessageBox.information(self, "No Data", "No crawled data available for summary.")
            return

        try:
            from intellicrawler.utils.summary_generator import CrawlerSummaryGenerator

            generator = CrawlerSummaryGenerator()
            summary = generator.generate_summary(self.scraped_data)

            # Display summary in insights tab
            self.insights_display.setText(summary)
            self.results_tabs.setCurrentIndex(2)

            self.status_label.setText("📊 Summary generated")
            self.add_ai_log("📊 Quick summary generated")

        except Exception as e:
            QMessageBox.critical(self, "Summary Error", f"Failed to generate summary: {str(e)}")
            self.add_ai_log(f"❌ Summary generation failed: {str(e)}")

    def export_data(self):
        """Export crawled data and analysis results"""
        if not self.scraped_data:
            QMessageBox.information(self, "No Data", "No data available for export.")
            return

        # Create export data
        export_data = {
            "export_info": {
                "export_date": datetime.now().isoformat(),
                "total_pages": len(self.scraped_data),
                "crawler_version": "2.0.0",
                "has_analysis": bool(self.analysis_results)
            },
            "crawled_data": self.scraped_data,
            "analysis_results": self.analysis_results
        }

        # Get export file path
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_filename = f"intellicrawler_export_{timestamp}.json"

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "💾 Export IntelliCrawler Data",
            default_filename,
            "JSON Files (*.json);;All Files (*)"
        )

        if file_path:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(export_data, f, indent=2, ensure_ascii=False)

                QMessageBox.information(
                    self,
                    "Export Successful",
                    f"Data exported successfully to:\n{file_path}"
                )

                self.status_label.setText(f"💾 Data exported to {os.path.basename(file_path)}")
                self.add_browser_log(f"💾 Data exported: {file_path}")

            except Exception as e:
                QMessageBox.critical(self, "Export Error", f"Failed to export data: {str(e)}")
                self.add_browser_log(f"❌ Export failed: {str(e)}")

    def update_data_table(self):
        """Update the crawled data table"""
        self.data_table.setRowCount(len(self.scraped_data))

        for row, item in enumerate(self.scraped_data):
            url = item.get('url', '')
            title = item.get('title', 'Untitled')
            content = item.get('content', '')
            status = item.get('status', 'Crawled')

            # Truncate content for preview
            content_preview = content[:100] + "..." if len(content) > 100 else content

            self.data_table.setItem(row, 0, QTableWidgetItem(url))
            self.data_table.setItem(row, 1, QTableWidgetItem(title))
            self.data_table.setItem(row, 2, QTableWidgetItem(content_preview))
            self.data_table.setItem(row, 3, QTableWidgetItem(status))

    def update_statistics(self, pages_found=None, pages_processed=None, ai_decisions=None, data_size=None):
        """Update live statistics"""
        if pages_found is None:
            pages_found = len(self.scraped_data)
        if pages_processed is None:
            pages_processed = len([item for item in self.scraped_data if item.get('status') == 'Processed'])
        if ai_decisions is None:
            ai_decisions = len([log for log in self.browser_activity_log if '🤖' in log])
        if data_size is None:
            data_size = len(json.dumps(self.scraped_data).encode('utf-8')) // 1024  # KB

        self.pages_found_label.setText(f"Pages Found: {pages_found}")
        self.pages_processed_label.setText(f"Pages Processed: {pages_processed}")
        self.ai_decisions_label.setText(f"AI Decisions: {ai_decisions}")
        self.data_saved_label.setText(f"Data Saved: {data_size} KB")

    def update_insights(self, analysis_results):
        """Update the insights display"""
        insights = []

        # Basic statistics
        insights.append("📊 CONTENT INSIGHTS\n" + "="*50)
        insights.append(f"Total Pages Analyzed: {len(self.scraped_data)}")

        if analysis_results:
            # Add analysis-specific insights
            if 'word_count' in analysis_results:
                insights.append(f"Total Word Count: {analysis_results['word_count']:,}")

            if 'keywords' in analysis_results:
                keywords = analysis_results['keywords'][:10]  # Top 10
                insights.append(f"\nTop Keywords: {', '.join(keywords)}")

            if 'sentiment' in analysis_results:
                sentiment = analysis_results['sentiment']
                insights.append(f"\nOverall Sentiment: {sentiment}")

            if 'topics' in analysis_results:
                topics = analysis_results['topics'][:5]  # Top 5
                insights.append(f"\nMain Topics: {', '.join(topics)}")

        # URL analysis
        domains = set()
        for item in self.scraped_data:
            url = item.get('url', '')
            if url:
                try:
                    from urllib.parse import urlparse
                    domain = urlparse(url).netloc
                    domains.add(domain)
                except:
                    pass

        insights.append(f"\nDomains Crawled: {len(domains)}")
        if domains:
            insights.append(f"Domains: {', '.join(list(domains)[:5])}")

        self.insights_display.setText('\n'.join(insights))

    def filter_results(self):
        """Filter results based on search input"""
        search_text = self.search_input.text().lower()

        for row in range(self.data_table.rowCount()):
            show_row = False

            for col in range(self.data_table.columnCount()):
                item = self.data_table.item(row, col)
                if item and search_text in item.text().lower():
                    show_row = True
                    break

            self.data_table.setRowHidden(row, not show_row)

    def add_browser_log(self, message):
        """Add message to browser activity log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.browser_activity_log.append(log_entry)
        self.browser_log.append(log_entry)

        # Auto-scroll to bottom
        cursor = self.browser_log.textCursor()
        cursor.movePosition(cursor.End)
        self.browser_log.setTextCursor(cursor)

    def add_ai_log(self, message):
        """Add message to AI decision log"""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}"
        self.ai_log.append(log_entry)

        # Auto-scroll to bottom
        cursor = self.ai_log.textCursor()
        cursor.movePosition(cursor.End)
        self.ai_log.setTextCursor(cursor)

    def update_real_time_display(self):
        """Update real-time display elements"""
        if self.is_running:
            # Update progress label
            if self.scraped_data:
                self.progress_label.setText(f"Crawling in progress... ({len(self.scraped_data)} pages found)")

            # Update statistics
            self.update_statistics()

    def update_session_timer(self):
        """Update session timer display"""
        if self.session_start_time:
            elapsed = datetime.now() - self.session_start_time
            hours, remainder = divmod(int(elapsed.total_seconds()), 3600)
            minutes, seconds = divmod(remainder, 60)
            self.session_timer_label.setText(f"⏱️ Session: {hours:02d}:{minutes:02d}:{seconds:02d}")

    def closeEvent(self, event):
        """Handle tab close event"""
        if self.is_running:
            reply = QMessageBox.question(
                self,
                "Crawling in Progress",
                "Crawling is currently in progress. Do you want to stop and close?",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.stop_crawling()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()
