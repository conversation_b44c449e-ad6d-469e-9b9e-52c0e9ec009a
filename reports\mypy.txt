supercrawler\resources\create_icon.py:12: error: Skipping analyzing "PIL": module is installed, but missing library stubs or py.typed marker  [import-untyped]
supercrawler\resources\create_icon.py:12: note: See https://mypy.readthedocs.io/en/stable/running_mypy.html#missing-imports
supercrawler\utils\summary_generator.py:18: error: Incompatible default for argument "input_file" (default has type "None", argument has type "str")  [assignment]
supercrawler\utils\summary_generator.py:18: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
supercrawler\utils\summary_generator.py:18: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
supercrawler\utils\summary_generator.py:18: error: Incompatible default for argument "data" (default has type "None", argument has type "list[dict[Any, Any]]")  [assignment]
supercrawler\utils\summary_generator.py:232: error: "object" has no attribute "append"  [attr-defined]
supercrawler\utils\summary_generator.py:245: error: Incompatible default for argument "output_path" (default has type "None", argument has type "str")  [assignment]
supercrawler\utils\summary_generator.py:245: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
supercrawler\utils\summary_generator.py:245: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
supercrawler\utils\summary_generator.py:261: error: Incompatible default for argument "output_path" (default has type "None", argument has type "str")  [assignment]
supercrawler\utils\summary_generator.py:261: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
supercrawler\utils\summary_generator.py:261: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
supercrawler\utils\summary_generator.py:277: error: Incompatible default for argument "output_dir" (default has type "None", argument has type "str")  [assignment]
supercrawler\utils\summary_generator.py:277: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
supercrawler\utils\summary_generator.py:277: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
supercrawler\utils\summary_generator.py:287: error: Argument 1 to "save_human_summary" of "CrawlerSummaryGenerator" has incompatible type "str | None"; expected "str"  [arg-type]
supercrawler\utils\summary_generator.py:288: error: Argument 1 to "save_ai_optimized_json" of "CrawlerSummaryGenerator" has incompatible type "str | None"; expected "str"  [arg-type]
supercrawler\utils\summary_generator.py:295: error: Incompatible default for argument "output_dir" (default has type "None", argument has type "str")  [assignment]
supercrawler\utils\summary_generator.py:295: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
supercrawler\utils\summary_generator.py:295: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
supercrawler\data_export.py:4: error: Library stubs not installed for "pandas"  [import-untyped]
supercrawler\data_export.py:4: note: Hint: "python3 -m pip install pandas-stubs"
supercrawler\utils\text_processor.py:178: error: Need type annotation for "chunks" (hint: "chunks: list[<type>] = ...")  [var-annotated]
supercrawler\utils\text_processor.py:180: error: Need type annotation for "current_pages" (hint: "current_pages: list[<type>] = ...")  [var-annotated]
supercrawler\ui\proxy_scraper_tab_fixed.py:28: error: Cannot find implementation or library stub for module named "proxy_scraper_simple"  [import-not-found]
supercrawler\ui\universal_website_handler.py:17: error: Library stubs not installed for "bs4"  [import-untyped]
supercrawler\ui\universal_website_handler.py:21: error: Skipping analyzing "urllib3.util.retry": module is installed, but missing library stubs or py.typed marker  [import-untyped]
supercrawler\crawler.py:2: error: Library stubs not installed for "bs4"  [import-untyped]
supercrawler\ui\music_player_tab.py:26: error: Cannot find implementation or library stub for module named "librosa"  [import-not-found]
supercrawler\ui\music_player_tab.py:27: error: Library stubs not installed for "scipy.signal"  [import-untyped]
supercrawler\ui\music_player_tab.py:27: note: Hint: "python3 -m pip install scipy-stubs"
supercrawler\ui\music_player_tab.py:27: note: (or run "mypy --install-types" to install all missing stub packages)
supercrawler\ui\music_player_tab.py:27: error: Library stubs not installed for "scipy"  [import-untyped]
supercrawler\ai_integration.py:1900: error: Name "stop" already defined on line 1834  [no-redef]
supercrawler\ai_integration.py:2019: error: Library stubs not installed for "bs4"  [import-untyped]
supercrawler\ui\ai_chat_tab.py:23: error: Incompatible default for argument "reasoning_content" (default has type "None", argument has type "str")  [assignment]
supercrawler\ui\ai_chat_tab.py:23: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
supercrawler\ui\ai_chat_tab.py:23: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
supercrawler\ui\ai_chat_tab.py:24: error: Incompatible default for argument "tool_calls" (default has type "None", argument has type "list[Any]")  [assignment]
supercrawler\ui\ai_chat_tab.py:24: note: PEP 484 prohibits implicit Optional. Accordingly, mypy has changed its default to no_implicit_optional=True
supercrawler\ui\ai_chat_tab.py:24: note: Use https://github.com/hauntsaninja/no_implicit_optional to automatically upgrade your codebase
supercrawler\ui\ai_chat_tab.py:24: error: Incompatible default for argument "name" (default has type "None", argument has type "str")  [assignment]
supercrawler\ui\proxy_scraper_tab.py:19: error: Skipping analyzing "urllib3.util.retry": module is installed, but missing library stubs or py.typed marker  [import-untyped]
supercrawler\ui\proxy_scraper_tab.py:30: error: Library stubs not installed for "bs4"  [import-untyped]
supercrawler\ui\proxy_scraper_tab.py:30: note: Hint: "python3 -m pip install types-beautifulsoup4"
supercrawler\main.py:16: error: Skipping analyzing "winshell": module is installed, but missing library stubs or py.typed marker  [import-untyped]
supercrawler\main.py:17: error: Library stubs not installed for "win32com.client"  [import-untyped]
supercrawler\main.py:17: note: Hint: "python3 -m pip install types-pywin32"
Found 29 errors in 12 files (checked 34 source files)
