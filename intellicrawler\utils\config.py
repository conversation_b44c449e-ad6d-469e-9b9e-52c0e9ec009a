import os
import json
from intellicrawler.utils.logger import get_logger

# Try to import dotenv, handle gracefully if not available
try:
    from dotenv import load_dotenv
    DOTENV_AVAILABLE = True
except ImportError:
    DOTENV_AVAILABLE = False

CONFIG_DIR = os.path.expanduser("~/.intellicrawler")
CONFIG_FILE = os.path.join(CONFIG_DIR, "config.json")
ENV_FILE = os.path.join(CONFIG_DIR, ".env")
ROOT_ENV_FILE = ".env"  # Project root .env file

# Removed local get_logger to avoid circular imports

def load_env_variables():
    """Load environment variables from .env files"""
    logger = get_logger()
    
    if not DOTENV_AVAILABLE:
        logger.warning("python-dotenv not available. Install it for .env file support")
        return {}
    
    env_vars = {}
    
    # List of .env files to check
    env_files = [
        (ROOT_ENV_FILE, "project root"),
        (ENV_FILE, "user config directory")
    ]
    
    for env_file_path, location_name in env_files:
        if os.path.exists(env_file_path):
            try:
                logger.info(f"Attempting to load .env file from {location_name}: {env_file_path}")
                with open(env_file_path, 'r', encoding='utf-8-sig') as f:
                    load_dotenv(stream=f)
                logger.info(f"Successfully loaded .env file from {location_name}")
            except UnicodeDecodeError:
                logger.error(
                    f"CRITICAL: Failed to decode .env file at {env_file_path}. "
                    "The file may have an incorrect encoding (e.g., UTF-16 instead of UTF-8). "
                    "SuperCrawler will skip loading this file. Please re-save it as UTF-8."
                )
            except Exception as e:
                logger.error(f"Failed to load .env file from {location_name} due to an unexpected error: {e}")

    # Get relevant environment variables
    env_vars = {
        "deepseek_api_key": os.getenv("DEEPSEEK_API_KEY", ""),
        "chrome_path": os.getenv("CHROME_PATH", ""),
        "user_agent": os.getenv("USER_AGENT", "IntelliCrawler/2.0 (AI-powered web analysis)"),
        "max_pages_default": int(os.getenv("MAX_PAGES_DEFAULT", "100")),
        "crawl_depth_default": int(os.getenv("CRAWL_DEPTH_DEFAULT", "3")),
        "delay_default": float(os.getenv("DELAY_DEFAULT", "1.0")),
    }
    
    # Log API key status
    if env_vars["deepseek_api_key"]:
        logger.info(f"Loaded API key from environment (length: {len(env_vars['deepseek_api_key'])})")
    
    return env_vars

def create_env_template():
    """Create a .env template file"""
    logger = get_logger()
    
    env_template = """# IntelliCrawler Environment Variables
# Copy this file to .env and fill in your values
# DO NOT commit .env files to version control!

# DeepSeek API Key (required for AI features)
DEEPSEEK_API_KEY=your_deepseek_api_key_here

# Chrome browser path (optional, auto-detected if empty)
CHROME_PATH=

# User agent string for web requests
USER_AGENT=IntelliCrawler/2.0 (AI-powered web analysis)

# Default crawling parameters
MAX_PAGES_DEFAULT=100
CRAWL_DEPTH_DEFAULT=3
DELAY_DEFAULT=1.0
"""
    
    try:
        # Create .env template in config directory
        env_template_file = os.path.join(CONFIG_DIR, ".env.template")
        with open(env_template_file, 'w', encoding='utf-8') as f:
            f.write(env_template)
        logger.info(f"Created .env template at: {env_template_file}")
        
        # Also create in project root if we're in the project directory
        if os.path.exists("supercrawler"):  # We're in project root
            root_template = ".env.template"
            with open(root_template, 'w', encoding='utf-8') as f:
                f.write(env_template)
            logger.info(f"Created .env template at: {root_template}")
            
        return True
    except Exception as e:
        logger.error(f"Error creating .env template: {str(e)}")
        return False

def create_default_config():
    """Create default configuration file"""
    logger = get_logger()
    logger.info("Creating default configuration")
    
    # Create config directory if it doesn't exist
    os.makedirs(CONFIG_DIR, exist_ok=True)
    
    # Default configuration
    default_config = {
        "deepseek_api_key": "",
        "create_shortcut": True,
        "max_pages_default": 100,
        "crawl_depth_default": 4,  # Increased from 3 to 4 for better documentation coverage
        "dynamic_default": False,
        "delay_default": 1.0,
        "user_agent": "IntelliCrawler/2.0 (AI-powered web analysis)",
        "theme": "dark",
        "export_formats": ["json", "csv", "excel", "xml", "text"],
        "report_formats": ["md", "pdf", "docx"],
        "log_level": "INFO"  # New: for logger toggle
    }
    
    # Write default config
    with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(default_config, f, ensure_ascii=False, indent=2)
    
    # Create .env template
    create_env_template()
    
    return default_config

def load_config():
    """Load configuration from .env files and config.json (env takes priority)"""
    logger = get_logger()
    
    try:
        # Ensure config directory exists
        if not os.path.exists(CONFIG_DIR):
            logger.info(f"Creating config directory: {CONFIG_DIR}")
            os.makedirs(CONFIG_DIR, exist_ok=True)
        
        # Start with default config
        config = create_default_config() if not os.path.exists(CONFIG_FILE) else {}
        
        # Load from config.json if it exists
        if os.path.exists(CONFIG_FILE):
            try:
                with open(CONFIG_FILE, 'r', encoding='utf-8-sig') as f:
                    json_config = json.load(f)
                config.update(json_config)
                logger.info("Configuration loaded from config.json")
            except json.JSONDecodeError:
                logger.error(f"Invalid JSON in config file: {CONFIG_FILE}")
                config = create_default_config()
            except Exception as e:
                logger.error(f"Error reading config file: {str(e)}")
                config = create_default_config()
        
        # Load environment variables (.env files take priority)
        env_vars = load_env_variables()
        if env_vars:
            # Only override config values if env vars are not empty
            for key, value in env_vars.items():
                if value:  # Only update if env var has a value
                    config[key] = value
            logger.info("Environment variables loaded and merged with config")
        
        # Log final API key status
        api_key = config.get("deepseek_api_key", "")
        if api_key:
            logger.info(f"Final API key loaded (length: {len(api_key)})")
        else:
            logger.warning("No API key found in config or environment")
            
        return config
        
    except Exception as e:
        logger.error(f"Error in load_config: {str(e)}")
        # Return a minimal default config
        return {
            "deepseek_api_key": "",
            "user_agent": "IntelliCrawler/2.0 (AI-powered web analysis)",
        }

def save_config(config):
    """Save configuration to file"""
    logger = get_logger()
    
    try:
        # Create config directory if it doesn't exist
        if not os.path.exists(CONFIG_DIR):
            logger.info(f"Creating config directory: {CONFIG_DIR}")
            os.makedirs(CONFIG_DIR, exist_ok=True)
        
        # Check write permissions
        try:
            test_file = os.path.join(CONFIG_DIR, ".test_write")
            with open(test_file, 'w') as f:
                f.write("test")
            os.remove(test_file)
            logger.info("Write permission verified for config directory")
        except Exception as perm_err:
            logger.error(f"Write permission error in config directory: {str(perm_err)}")
            return False
        
        # Save API key status for debugging
        api_key = config.get("deepseek_api_key", "")
        if api_key:
            logger.info(f"Saving API key to config (length: {len(api_key)})")
        else:
            logger.warning("No API key to save in config")
            
        # Write config to file
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
            logger.info(f"Configuration saved successfully to: {CONFIG_FILE}")
        
        # Verify the file was written
        if os.path.exists(CONFIG_FILE):
            return True
        else:
            logger.error(f"Config file was not created: {CONFIG_FILE}")
            return False
    except Exception as e:
        logger.error(f"Error saving config: {str(e)}")
        return False 